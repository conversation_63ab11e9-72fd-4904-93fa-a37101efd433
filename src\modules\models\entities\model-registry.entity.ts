import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { InputModalityEnum, OutputModalityEnum, SamplingParameterEnum, FeatureEnum } from '../constants/model-capabilities.enum';
import { ProviderEnum } from '../constants';

/**
 * Entity đại diện cho bảng model_registry trong cơ sở dữ liệu
 * Lưu thông tin cấu hình mô hình (input/output, sampling, feature, ...)
 */
@Entity('model_registry')
export class ModelRegistry {
  /**
   * UUID của registry, sinh tự động
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Nhà cung cấp model
   */
  @Column({ name: 'provider', type: 'enum', enum: ProviderEnum, default: ProviderEnum.OPENAI, nullable: false })
  provider: ProviderEnum;

  /**
   * Tên mẫu đại diện của model
   */
  @Column({ name: 'model_name_pattern', type: 'varchar', length: 255, nullable: false })
  modelNamePattern: string;

  /**
   * Các loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @Column({ name: 'input_modalities', type: 'jsonb', default: '[]' })
  inputModalities: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @Column({ name: 'output_modalities', type: 'jsonb', default: '[]' })
  outputModalities: OutputModalityEnum[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @Column({ name: 'sampling_parameters', type: 'jsonb', default: '[]' })
  samplingParameters: SamplingParameterEnum[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @Column({ name: 'features', type: 'jsonb', default: '[]' })
  features: FeatureEnum[];

  /**
   * Thời gian tạo (epoch millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Người tạo (liên kết employees)
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời gian xóa mềm (soft delete)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Người thực hiện xóa
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy: number | null;
}
