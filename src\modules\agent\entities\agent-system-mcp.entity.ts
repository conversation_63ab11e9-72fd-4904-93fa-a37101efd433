import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agent_system_mcp trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa agents_system và mcp_systems
 */
@Entity('agent_system_mcp')
export class AgentSystemMcp {
  /**
   * ID của agents_system
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' })
  agentId: string;

  /**
   * ID của mcp_systems
   */
  @PrimaryColumn({ name: 'mcp_id', type: 'uuid' })
  mcpId: string;
}
