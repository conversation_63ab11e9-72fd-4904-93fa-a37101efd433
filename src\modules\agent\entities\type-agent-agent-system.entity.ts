import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng type_agent_agent_system trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa type_agents và agents_system
 */
@Entity('type_agent_agent_system')
export class TypeAgentAgentSystem {
  /**
   * ID của type_agents
   */
  @PrimaryColumn({ name: 'type_id', type: 'integer' })
  typeId: number;

  /**
   * ID của agents_system
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' })
  agentId: string;
}
